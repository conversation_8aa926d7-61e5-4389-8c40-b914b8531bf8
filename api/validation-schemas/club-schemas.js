'use strict';

const Joi = require('joi');

const {
    ZIP_MESSAGE,
    CA_ZIP_REG_EXP,
    US_ZIP_REG_EXP,
    USAV_REG_EXP,
    TEAM_USAV_SEASONALITY,
    EMAIL_REGEX,
    BM_ZIP_REG_EXP,
    CH_ZIP_REG_EXP,
    CO_ZIP_REG_EXP,
} = require('../lib/joi-constants');
const {
    US_COUNTRY_CODE,
    CA_COUNTRY_CODE,
    BM_COUNTRY_CODE,
    PL_COUNTRY_CODE,
    CH_COUNTRY_CODE,
    HN_COUNTRY_CODE,
    CO_COUNTRY_CODE,
} = require('../constants/common')

const CA_ZIP_VALIDATOR = Joi.string().pattern(CA_ZIP_REG_EXP).preferences(ZIP_MESSAGE);
const US_ZIP_VALIDATOR = Joi.string().pattern(US_ZIP_REG_EXP).preferences(ZIP_MESSAGE);
const BM_ZIP_VALIDATOR = Joi.string().pattern(BM_ZIP_REG_EXP).preferences(ZIP_MESSAGE);
const CH_ZIP_VALIDATOR = Joi.string().pattern(CH_ZIP_REG_EXP).preferences(ZIP_MESSAGE);
const CO_ZIP_VALIDATOR = Joi.string().pattern(CO_ZIP_REG_EXP).preferences(ZIP_MESSAGE);

var customRegexMsg = function (msg) {
    msg = msg || 'is Invalid';
    return {
        messages: {
            'string.regex': {
                base: msg
            }
        }
    };
};

module.exports = {
    master_team: Joi.object().keys({
        master_team_id          : Joi.number().label('Team identifier'),
        team_name               : Joi.string().min(3).max(200).required().label('Team Name'),
        gender                  : Joi.string().valid('male', 'female', 'coed').required().label('Gender'),
        age                     : [Joi.number().required(), Joi.string().pattern(/^\d{2,2}$/).required()],
        rank                    : [Joi.string().required(), Joi.number().required()],
        sport_variation_id      : Joi.number().required().label('Sport Variation'),
        sport_id                : Joi.number(),
        seasonality             : Joi.alternatives().conditional('has_usav_sanctioning', {
                                        is: true,
                                        then: Joi.string()
                                            .valid(TEAM_USAV_SEASONALITY.FULL, TEAM_USAV_SEASONALITY.LOCAL)
                                            .required(),
                                        otherwise: Joi.string()
                                            .allow(null)
                                            .valid(TEAM_USAV_SEASONALITY.FULL, TEAM_USAV_SEASONALITY.LOCAL)
                                            .optional()
                                    }).label('USAV Seasonality'),
        organization_code       : Joi.alternatives().conditional('master_team_id', {
                                        is          : Joi.number(),
                                        then        : Joi.string().min(11).max(12).required(),
                                        otherwise   : Joi.string().allow(null).min(11).max(12)
                                    }).label('Organization team code'),
        has_usav_sanctioning    : Joi.boolean().required().strip()
    }),
    update_club: Joi.object().keys({
        club_name               : Joi.string().required().max(200).label('Club Name'),
        team_prefix             : Joi.string().label('Team Prefix'),
        address                 : Joi.string().max(200).required().label('Address'),
        city                    : Joi.string().max(200).required().label('City'),
        state                   : Joi.string().allow(null).length(2).label('State'),
        zip                     : Joi.string().required().max(10).label('Zip'),
        country                 : Joi.string().required().length(2).label('Country'),
        has_male_teams          : Joi.boolean().allow(null).label('Male Team Gender'),
        has_female_teams        : Joi.boolean().allow(null).label('Female Team Gender'),
        has_coed_teams          : Joi.boolean().allow(null).label('Coed Team Gender'),
        director_first          : Joi.string().required().label('Director First'),
        director_last           : Joi.string().required().label('Director Last'),
        director_birthdate      : Joi.string().required()
                                    .replace(
                                        /^(0?[1-9]|1[0-2])\/(0?[1-9]|1[0-9]|2[0-9]|3[0-1])\/(19[4-9][0-9]|20[0-9][0-9])/g,
                                        '$3-$1-$2'
                                    ).label('Director Birthdate'),
        director_email          : Joi.string().pattern(EMAIL_REGEX).required().label('Director Email'),
        administrative_email    : Joi.string()
            .empty(null)
            .pattern(EMAIL_REGEX)
            .required()
            .invalid(Joi.ref('director_email'))
            .insensitive()
            .messages({
                'any.invalid': 'Should be different from director email',
            })
            .label('Administrative Email'),
        director_phone          : Joi.alternatives().conditional('country', {
                                    is :        PL_COUNTRY_CODE,
                                    then:       Joi.string().min(9).required().replace(/\D/g, ''),
                                    otherwise: Joi.alternatives().conditional('country', {
                                        is: HN_COUNTRY_CODE,
                                        then: Joi.string().min(8).required().replace(/\D/g, ''),
                                        otherwise: Joi.string().min(10).required().replace(/\D/g, ''),
                                    }),
                                }).label('Director Phone'),
        director_gender         : Joi.string().required().valid('male', 'female', 'non-binary').label('Director Gender'),
        director_usav_code      : Joi.string().allow(null).allow('').pattern(USAV_REG_EXP).preferences(customRegexMsg()).label('Director USAV'),
        sport_sanctionings      : Joi.array().items(Joi.number()).unique().optional().label('Sport Sanctionings').strip(),
        code                    : Joi.string().pattern(/^(\d|\w)+$/).preferences(customRegexMsg('should contain only numbers or/and letters')).max(5).label('Club Code')
            .when('has_usav_sanctioning', {
                is          : true,
                then        : Joi.string().required(),
                otherwise   : Joi.string().allow(null, '').truncate() // If club is not using USAV, Club Code value will be truncated.
            }),
        region              : Joi.when('has_usav_sanctioning', {
                is          : true,
                then        : Joi.string().allow(null).max(10).default(null).label('Region'),
                otherwise   : Joi.string().allow(null, '').truncate(),
        }),
        aau_club_code       : Joi.string().label('Club AAU Code')
            .when('has_aau_sanctioning', {
                is          : true,
                then        : Joi.string().required(),
                otherwise   : Joi.string().allow(null, '').truncate()
            }).label('AAU Club Code'),
        aau_primary_membership_id   : Joi.string().label('AAU Representative Membership Id')
            .when('has_aau_sanctioning', {
                is          : true,
                then        : Joi.string().required(),
                otherwise   : Joi.string().allow(null, '').optional()
            }).label('AAU Primary Membership ID'),
        aau_primary_zip             : Joi
            .when('has_aau_sanctioning', {
                is          : true,
                then        : Joi.alternatives().conditional(
                    Joi.ref('country'), {
                        switch: [
                            {
                                is: US_COUNTRY_CODE,
                                then: US_ZIP_VALIDATOR,
                            },
                            {
                                is: CA_COUNTRY_CODE,
                                then: CA_ZIP_VALIDATOR,
                            },
                            {
                                is: BM_COUNTRY_CODE,
                                then: BM_ZIP_VALIDATOR,
                            },
                            {
                                is: CH_COUNTRY_CODE,
                                then: CH_ZIP_VALIDATOR,
                            },
                            {
                                is: CO_COUNTRY_CODE,
                                then: CO_ZIP_VALIDATOR,
                            },
                        ],
                    })
                    .required(),
                otherwise   : Joi.alternatives().conditional(
                    Joi.ref('country'), {
                        switch: [
                            {
                                is: US_COUNTRY_CODE,
                                then: US_ZIP_VALIDATOR,
                            },
                            {
                                is: CA_COUNTRY_CODE,
                                then: CA_ZIP_VALIDATOR,
                            },
                            {
                                is: BM_COUNTRY_CODE,
                                then: BM_ZIP_VALIDATOR,
                            },
                            {
                                is: CH_COUNTRY_CODE,
                                then: CH_ZIP_VALIDATOR,
                            },
                        ],
                    })
                    .optional()
                    .allow(null, '')
            }).label('Primary Representative Zip Code'),
        has_usav_sanctioning    : Joi.boolean(),
        has_aau_sanctioning     : Joi.boolean(),
        not_completed           : Joi.boolean().optional().allow(null).label('Not completed profile')
    }),
    create_club: Joi.object().keys({
        club_name               : Joi.string().required().max(200).label('Club Name'),
        team_prefix             : Joi.string().required().label('Team Prefix'),
        code                    : Joi.string().pattern(/^(\d|\w)+$/).preferences(customRegexMsg('should contain only numbers or/and letters')).max(5).label('Club Code')
                                    .when('has_usav_sanctioning', {
                                        is          : true,
                                        then        : Joi.string().required(),
                                        otherwise   : Joi.string().allow(null, '').truncate() // If club is not using USAV, Club Code value will be truncated.
                                    }),
        aau_club_code           : Joi.string().label('Club AAU Code')
                                    .when('has_aau_sanctioning', {
                                        is          : true,
                                        then        : Joi.string().required(),
                                        otherwise   : Joi.string().allow(null, '').truncate()
                                    }),
        aau_primary_membership_id   : Joi.string().label('AAU Representative Membership Id')
                                        .when('has_aau_sanctioning', {
                                            is          : true,
                                            then        : Joi.string().required(),
                                            otherwise   : Joi.string().allow(null, '').truncate()
                                        }),
        aau_primary_zip             : Joi
            .when('has_aau_sanctioning', {
                is          : true,
                then        : Joi.alternatives().conditional(
                    Joi.ref('country'), {
                        switch: [
                            {
                                is: US_COUNTRY_CODE,
                                then: US_ZIP_VALIDATOR,
                            },
                            {
                                is: CA_COUNTRY_CODE,
                                then: CA_ZIP_VALIDATOR,
                            },
                            {
                                is: BM_COUNTRY_CODE,
                                then: BM_ZIP_VALIDATOR,
                            },
                            {
                                is: CH_COUNTRY_CODE,
                                then: CH_ZIP_VALIDATOR,
                            },
                            {
                                is: CO_COUNTRY_CODE,
                                then: CO_ZIP_VALIDATOR,
                            },
                        ],
                    })
                    .required(),
                otherwise   : Joi.alternatives().conditional(
                    Joi.ref('country'), {
                        switch: [
                            {
                                is: US_COUNTRY_CODE,
                                then: US_ZIP_VALIDATOR,
                            },
                            {
                                is: CA_COUNTRY_CODE,
                                then: CA_ZIP_VALIDATOR,
                            },
                            {
                                is: BM_COUNTRY_CODE,
                                then: BM_ZIP_VALIDATOR,
                            },
                            {
                                is: CH_COUNTRY_CODE,
                                then: CH_ZIP_VALIDATOR,
                            },
                        ],
                    })
                    .optional()
                    .allow(null, '')
            }).label('Primary Representative Zip Code'),
        sport_id                : Joi.number().required().label('Club Sport'),
        address                 : Joi.string().max(200).required().label('Address'),
        city                    : Joi.string().max(200).required().label('City'),
        state                   : Joi.string().allow(null).length(2).label('State'),
        zip                     : Joi.string().required().max(10).label('Zip'),
        country                 : Joi.string().required().length(2).label('Country'),
        region                  : Joi.string().allow(null).max(10).default(null).label('Region'),
        sport_sanctionings      : Joi.array().items(Joi.number()).unique().required().label('Sport Sanctionings'),
        sport_variations        : Joi.array().items(Joi.number()).required().label('Sport Variations'),
        has_male_teams          : Joi.boolean().allow(null).label('Male Team Gender'),
        has_female_teams        : Joi.boolean().allow(null).label('Female Team Gender'),
        has_coed_teams          : Joi.boolean().allow(null).label('Coed Team Gender'),
        director_first          : Joi.string().required().label('Director First'),
        director_last           : Joi.string().required().label('Director Last'),
        director_birthdate      : Joi.string().required()
                                    .replace(
                                        /^(0?[1-9]|1[0-2])\/(0?[1-9]|1[0-9]|2[0-9]|3[0-1])\/(19[4-9][0-9]|20[0-9][0-9])/g,
                                        '$3-$1-$2'
                                    ).label('Director Birthdate'),
        director_email          : Joi.string().pattern(EMAIL_REGEX).required().label('Director Email'),
        administrative_email    : Joi.string()
            .empty(null)
            .pattern(EMAIL_REGEX)
            .required()
            .invalid(Joi.ref('director_email'))
            .insensitive()
            .messages({
                'any.invalid': 'Should be different from director email',
            })
            .label('Administrative Email'),
        director_phone          : Joi.alternatives().conditional('country', {
                                    is :        PL_COUNTRY_CODE,
                                    then:       Joi.string().min(9).required().replace(/\D/g, ''),
                                    otherwise: Joi.alternatives().conditional('country', {
                                        is: HN_COUNTRY_CODE,
                                        then: Joi.string().min(8).required().replace(/\D/g, ''),
                                        otherwise: Joi.string().min(10).required().replace(/\D/g, ''),
                                    }),
                                }).label('Director Phone'),
        director_gender         : Joi.string().required().valid('male', 'female', 'non-binary').label('Director Gender'),
        director_usav_code      : Joi.string()
                                    .when('has_usav_sanctioning', {
                                            is          : true,
                                            then        : Joi.string().pattern(USAV_REG_EXP).preferences(customRegexMsg()).allow(null, ''),
                                            otherwise   : Joi.string().pattern(USAV_REG_EXP).preferences(customRegexMsg()).allow(null, '')
                                    }).label('Director USAV'),
        has_usav_sanctioning    : Joi.boolean(),
        has_aau_sanctioning     : Joi.boolean(),
    }),
    master_athlete: Joi.object().keys({
        height                  : Joi.alternatives().try(Joi.number(), Joi.string(), null).label('Height'),
        address                 : Joi.string().allow(null).label('Address'),
        age                     : Joi.alternatives().try(Joi.number(), Joi.string(), null).label('Age'),
        zip                     : Joi.when('country', {
                                     is: 'CA',
                                     then: CA_ZIP_VALIDATOR,
                                     otherwise: US_ZIP_VALIDATOR,
                                    }).label('Zip'),
        phonep                  : Joi.string().allow(null).min(0).max(15).label('Parents Mobile Phone'),
        phonem                  : Joi.string().allow(null).min(0).max(15).label('Mobile Phone'),
        phoneh                  : Joi.string().allow(null).min(0).max(15).label('Home Phone'),
        city                    : Joi.string().min(1).max(100).label('City'),
        state                   : Joi.string().allow(null).min(2).max(2).label('State'),
        country                 : Joi.string().min(2).max(2).label('Country'),
        email                   : Joi.string().allow(null).min(0).max(100).label('Email'),
        gradyear                : Joi.number().label('HS Grad Year'),
        birthdate               : Joi.date().forbidden().label('Birthdate'),
        first                   : Joi.string().min(1).max(100).label('First Name'),
        master_team_id          : Joi.number().allow(null).label('Team'),
        sport_position_id       : Joi.number().allow(null).label('Position'),
        jersey                  : Joi.number().integer().positive().allow(null).label('Uniform'),
        aau_jersey              : Joi.number().integer().positive().allow(null).label('AAU Uniform'),
        nick                    : Joi.string().allow(null).label('Nickname'),
        address2                : Joi.string().allow(null).label('Address 2'),
        other_phone             : Joi.string().allow(null).label('Other Phone'),
        parent1_first           : Joi.string().allow(null).label('Parent 1 First Name'),
        parent1_last            : Joi.string().allow(null).label('Parent 1 Last Name'),
        parent1_email           : Joi.string().allow(null).label('Parent 1 Email'),
        parent2_first           : Joi.string().allow(null).label('Parent 2 First Name'),
        parent2_last            : Joi.string().allow(null).label('Parent 2 Last Name'),
        parent2_email           : Joi.string().allow(null).label('Parent 2 Email'),
        seasonality             : Joi.string().allow(null).optional().strip().label('Seasonality'),
        role                    : Joi.number().label('Athlete Role'),
    }),
    update_staff: Joi.object().keys({
        email                   : Joi.string().allow(null).pattern(EMAIL_REGEX).label('Email'),
        first                   : Joi.string().min(1).max(100).label('First Name'),
        nick                    : Joi.string().allow(null).label('Nickname'),
        phoneo                  : Joi.string().allow(null).label('Other Phone'),
        phonew                  : Joi.string().allow(null).label('Work Phone'),
        phone                   : Joi.string().allow(null).label('Mobile Phone'),
        phoneh                  : Joi.string().allow(null).label('Home Phone'),
        address                 : Joi.string().allow(null).label('Address'),
        address2                : Joi.string().allow(null).label('Address 2'),
        city                    : Joi.string().allow(null).label('City'),
        state                   : Joi.string().allow(null).length(2).label('State'),
        zip                     : Joi.string().allow(null).max(20).label('Zip'),
        birthdate               : Joi.date().forbidden().label('Birthdate'),
    })

}
