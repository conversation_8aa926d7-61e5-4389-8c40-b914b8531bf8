'use strict';

const co= require('co');
const moment= require('moment');

const schemas 		= require('../../validation-schemas/club-schemas');
const createSchema 	    = schemas.create_club;
const updateSchema 	    = schemas.update_club;

const {CA_COUNTRY_CODE, PL_COUNTRY_CODE} = require("../../constants/common");

class ClubService {
	constructor () {}

	get _DIRECTOR_ROLE_ID () {
		return 2;
	}

    get USAV_SANC_BODY () {
	    return 3;
    }

    get AAU_SANC_BODY () {
	    return 1;
    }

	get _DIRECTOR_SQL () {
		return (
			`WITH "update_director" AS (
                UPDATE "master_staff" ms 
                    SET "season"            = $2,
                    	"birthdate"         = $3,
                        "email"             = $4,
                        "first"             = $5,
                        "last"              = $6,
                        "gender"            = $7,
                        "phone"             = $8,
                        "organization_code" = $9,
                        "usav_number" 		= $10
                WHERE ms.master_club_id = $1
                    AND ms.usav_number = $10
                RETURNING 'update'::TEXT "action", ms."master_staff_id" "id"
             ), "insert_director" AS (
                INSERT INTO "master_staff" (
                    "master_club_id", "season", "birthdate", "email", "first", "last", "gender", "phone",
                    "organization_code", "usav_number"
                ) 
                SELECT $1, $2, $3, $4, $5, $6, $7, $8, $9, $10
                WHERE NOT EXISTS (
                    SELECT * FROM "update_director"
                )
                RETURNING 'create'::TEXT "action", "master_staff_id" "id"
             )
             SELECT * FROM "update_director" UNION ALL SELECT * FROM "insert_director"`
		);
	}

	get _DIRECTOR_FIELDS () {
		return [
			'director_birthdate', 'director_email', 'director_first',  
			'director_last', 'director_gender',  'director_phone', 
			'director_usav_code', 'membership_id'
		];
	}

	_clubCodeExists (code, region) {
		return Db.query(
			`SELECT mc."master_club_id" 
			 FROM "master_club" mc
			 WHERE mc."code" ILIKE $1 
			 	AND mc."region" ILIKE $2`,
			[code, region]
		).then(result => (result.rows.length > 0))
	}

    _clubAauCodeExists (code, membership, zip) {
        return Db.query(
            `SELECT mc."master_club_id" 
			 FROM "master_club" mc
			 WHERE mc."aau_club_code" ILIKE $1 
			 	AND mc."aau_primary_membership_id" ILIKE $2
			 	AND mc."aau_primary_zip" ILIKE $3`,
            [code, membership, zip]
        ).then(result => (result.rows.length > 0))
    }

	_findOrCreateClubOwner (userID) {
		return Db.query(
			`WITH "find" AS (
				SELECT co."club_owner_id" "id", mc."master_club_id"
				FROM "club_owner" co
				LEFT JOIN "master_club" mc 
        			ON mc.club_owner_id = co.club_owner_id
				WHERE co."user_id" = $1
			), "ins" AS (
				INSERT INTO "club_owner" ("user_id", "active")
				SELECT $1::INTEGER, TRUE
				WHERE NOT EXISTS (
					SELECT * FROM "find"
				)
				RETURNING "club_owner_id" "id", 0 "master_club_id"
			)
			SELECT * FROM "find" UNION ALL SELECT * FROM "ins"`,
			[userID]
		).then(result => result.rows[0])
	}

	_createClubRow (tr, club) {
		return tr.query(
            squel.insert().into('master_club')
            .setFields(club)
            .set('director_modified', 'NOW()', { dontQuote: true })
            .returning('*')
        )
        .then(result => result.rows[0].master_club_id)
        .then(masterClubID => {
        	if (!masterClubID) {
        		throw new Error('Internal Error on Club row creation');
        	}

        	return tr.query(
                squel.update().table('club_owner')
                .set('master_club_id',  masterClubID)
                .where('club_owner_id = ?', club.club_owner_id)
            ).then(result => {
	        	if (result.rowCount === 0) {
	        		throw new Error('Internal Error on Club Owner row modification')
	        	}	

	        	return masterClubID;
	        })
        })
	}
    // covered 😄👍
	_formatSports (masterClubID, list, fieldName) {
		return list.map(item => {
			let output = { master_club_id: masterClubID };

			output[fieldName] = item;

			return output;
		})
	}

	_saveSancAndVars (tr, masterClubID, sanctionings, variations) {
		return Promise.resolve().then(() => {
			let sqlQueriesList = [];	

			if (sanctionings.length) {
				let sancOutput 	= this._formatSports(masterClubID, sanctionings, 'sport_sanctioning_id');
				let query 		= squel.insert().into('master_club_sanctioning').setFieldsRows(sancOutput);
				sqlQueriesList.push(query);
			}	

			if (variations.length) {
				let varOutput 	= this._formatSports(masterClubID, variations, 'sport_variation_id');
				let query 		= squel.insert().into('master_club_sport_variation').setFieldsRows(varOutput);
				sqlQueriesList.push(query);
			}

			if (sqlQueriesList.length) {
				return Promise.all(sqlQueriesList.map(query => tr.query(query))).then(() => {})
			}
		});
	}

    async _updateTeamsCodes(tr, masterClubId, {code, region=null}) {
       if(!region) {
           region = 'XX';
        }
        if(typeof code !== 'string' || typeof region !== 'string' || region.length!== 2) {
            throw new Error('Invalid code or region: ' + JSON.stringify({code, region}));
        }

        const updateCodeStatement = (prefix = '') => {
            if(prefix) {
                prefix += '.';
            }

            return squel.str(
                `overlay(overlay(${prefix}organization_code placing ? from 10 for 2) placing ? from 4 for 5)`,
                region,
                code
            );
        }

        await tr.query(
            squel.update()
                .table('master_team', 'mt')
                .where('mt.master_club_id = ?', masterClubId)
                .where('mt.organization_code <> ?', updateCodeStatement('mt'))
                .from('master_team', 'ot')
                .where('ot.master_team_id = mt.master_team_id')
                .set('organization_code', updateCodeStatement('mt'))
                .returning('mt.master_team_id')
        );
        const {rows: updatedRosterTeams} = await tr.query(
            squel.update()
                .table('roster_team', 'rt')
                .from('roster_team', 'ort')
                .where('ort.roster_team_id = rt.roster_team_id')
                .from('master_team', 'mt')
                .where('mt.master_team_id = rt.master_team_id')
                .from('event', 'e')
                .where('e.event_id = rt.event_id')
                .where('e.date_start > (NOW() AT TIME ZONE e.timezone)')
                .where('e.season = ?', sails.config.sw_season.current)
                .where('rt.organization_code <> ?', updateCodeStatement('rt'))
                .where('mt.master_club_id = ?', masterClubId)
                .set('organization_code', updateCodeStatement('rt'))
                .returning('rt.roster_team_id', 'id')
                .returning('rt.event_id', 'event_id')
                .returning('ort.organization_code', 'old_value')
                .returning('rt.organization_code', 'new_value')
        );
        return {
            updatedRosterTeams,
        };
    }

	_prepareDirectorSQL (masterClubId, season, director) {
		let sql = this._DIRECTOR_SQL;

		let params = [masterClubId, season];

		this._DIRECTOR_FIELDS.forEach(field => {
			params.push(director[field]);
		})

		return { sql, params };
	}

	_upsertClubDirector (tr, masterClubID, season, director) {
		return co(function* () {
			let sqlData = this._prepareDirectorSQL(masterClubID, season, director);

			let result = yield (tr.query(sqlData.sql, sqlData.params));

			let masterStaffRow = result.rows[0];

			if (_.isEmpty(masterStaffRow)) {
				throw new Error('Error while processing Director Row');
			}

			yield (Promise.all([
		         tr.query(
		             squel.update().table('master_club')
		             .set('director_master_staff_id', masterStaffRow.id)
		             .where('master_club_id = ?', masterClubID)
		         ),
		         tr.query(
		             `INSERT INTO "master_staff_role" ("role_id", "master_staff_id")
		              SELECT $1, $2 
		              WHERE NOT EXISTS (
		                 SELECT * FROM "master_staff_role" 
		                 WHERE "role_id" = $1 AND "master_staff_id" = $2
		              )`,
		             [this._DIRECTOR_ROLE_ID, masterStaffRow.id]
		         )
		    ]).then(() => {}));
		}.bind(this));
	}

    async _updateRosterClubs(tr, masterClubId) {
        const { rows: updated_rc } = await tr.query(
            squel.update().table('roster_club', 'rc')
                .where('rc.master_club_id = ?', masterClubId)
                .from('master_club', 'mc')
                .where('mc.master_club_id = rc.master_club_id')
                .from('event', 'e')
                .where('rc.event_id = e.event_id')
                .where('e.deleted IS NULL')
                .where('e.date_end > NOW() AT TIME ZONE e.timezone')
                .set('club_name = mc.club_name')
                .set('zip = mc.zip')
                .set('country = mc.country')
                .set('state = mc.state')
                .set('region = mc.region')
                .set('city = mc.city')
                .set('address = mc.address')
                .set('code = mc.code')
                .with(
                    'old_data',
                    squel.select()
                        .from('roster_club', 'rc')
                        .where('rc.master_club_id = ?', masterClubId)
                        .join('event', 'e', 'e.event_id = rc.event_id')
                        .where('e.deleted IS NULL')
                        .where('e.date_end > NOW() AT TIME ZONE e.timezone')
                        .field('rc.roster_club_id')
                        .field('rc.address')
                        .field('rc.city')
                        .field('rc.state')
                        .field('rc.zip')
                )
                .from('old_data', 'orc')
                .where('rc.roster_club_id = orc.roster_club_id')
                .returning('e.event_id')
                .returning('rc.roster_club_id')
                .returning('rc.master_club_id')
                .returning('rc.distance_to_event', 'old_distance_to_event')
                .returning(`(
                    rc.address <> orc.address
                    OR rc.city <> orc.city
                    OR rc.state <> orc.state
                    OR rc.zip <> orc.zip
                )`, 'distance_changed')
        );

        for(const {event_id, roster_club_id, master_club_id, old_distance_to_event, distance_changed} of updated_rc) {
            if(distance_changed) {
                await this.geo.calculateDistanceToEvent(tr, roster_club_id, master_club_id, event_id);
                let fields = {
                    action: 'club.location.changed',
                    event_id,
                    roster_club_id,
                };
                if(old_distance_to_event) {
                    fields.comments = `Previous distance to Event ${old_distance_to_event}`;
                }
                await tr.query(
                    squel.insert().into('event_change')
                        .setFields(fields)
                );
            }
        }
    }

    __validateUsavMember (clubData) {
	    let params = {
            usav_code   : clubData.director_usav_code,
            first       : clubData.director_first,
            last        : clubData.director_last,
            gender      : clubData.director_gender,
            region      : clubData.region,
            club_code   : clubData.code,
            birthdate: {
                date    : clubData.director_birthdate,
                format  : 'YYYY-MM-DD'
            }
        };

        return SportEngineMemberService.validation.processMember(params, SportEngineMemberService.validation.MEMBER_TYPE.clubDirector)
            .then(data => {
                if(data.validationError) {
                    throw data.validationError;
                }
            })
    }

    clubHasSportSanctionings(club) {
	    return club.sport_sanctionings && club.sport_sanctionings.length;
    }

	createMasterClub (club, season, userID) {
		return co(function* () {
		    // Temporary variable to determine does club has USAV sanctioning.
            club.has_usav_sanctioning = club.sport_sanctionings.includes(this.USAV_SANC_BODY);
            club.has_aau_sanctioning = club.sport_sanctionings.includes(this.AAU_SANC_BODY);

			let validation = createSchema.validate(club);

			if (validation.error) {
				loggers.errors_log.error(validation.error);
				return Promise.reject({ validationErrors: validation.error.details });
			}

			let preparedClub = validation.value;

            if(preparedClub.country === PL_COUNTRY_CODE) {
                preparedClub.region = preparedClub.country;
            }

            if(club.has_usav_sanctioning) {
                let codeExists = yield (this._clubCodeExists(preparedClub.code, preparedClub.region));

                if (codeExists) {
                    return Promise.reject({ validation: 'Club USAV code must be unique within region' });
                }
            }
            else {
                preparedClub.region = 'XX';
                preparedClub.code = squel.str('nextval(?)', 'master_club_code');
            }

            if(club.has_aau_sanctioning) {
                let codeExists = yield (this._clubAauCodeExists(
                    preparedClub.aau_club_code, preparedClub.aau_primary_membership_id, preparedClub.aau_primary_zip));

                if (codeExists) {
                    return Promise.reject({ validation: 'Club AAU code must be unique' });
                }
            }

			if(preparedClub.director_usav_code && preparedClub.country !== CA_COUNTRY_CODE) {
                yield this.__validateUsavMember(preparedClub);
            }

			let clubOwner = yield (this._findOrCreateClubOwner(userID));

			if (!clubOwner.id) {
				return Promise.reject(new Error('Cannot find a club owner'));
			}

			if (clubOwner.master_club_id) {
				return Promise.reject(new Error('Club already Exists. If you do not see it, please, relogin.'));
			}

			preparedClub.club_owner_id          = clubOwner.id;
            preparedClub.profile_completed_at   = moment().format();

			delete preparedClub.has_usav_sanctioning;
			delete preparedClub.has_aau_sanctioning;

			let tr = yield (Db.begin());

			let masterClubID = yield (
				processNewRows.bind(this, tr, preparedClub)().catch(err => {
                    if(tr && !tr.isCommited) {
                        tr.rollback();
                    }
					throw err;
				})
			);

			yield (tr.commit());

			return {
				master_club_id 	: masterClubID, 
				club_owner_id 	: clubOwner.id 
			};
		}.bind(this));

		function processNewRows (tr, club) {
			/*jshint validthis:true */
			let self = this;

			return co(function* () {
				let masterClubID 
							= yield (self._createClubRow(tr, _.omit(club, 'sport_sanctionings', 'sport_variations')));

				yield (self._saveSancAndVars(tr, masterClubID, club.sport_sanctionings, club.sport_variations));

				if (club.director_usav_code) {
					let membership_id = WebpointUtility.getMembershipNumber(club.director_usav_code);

					yield (self._upsertClubDirector(tr, masterClubID, season, _.extend(club, { membership_id })));
				}

				return masterClubID;
			});
		}
	}

    updateMasterClub (clubData, masterClubId, clubOwnerId) {
        let tr;
        return (async () => {
            let tasks = [];
            // Temporary variable to determine does club has USAV sanctioning.
            let hasUsavSanctioning = false;
            if (this.clubHasSportSanctionings(clubData)) {
                hasUsavSanctioning = clubData.has_usav_sanctioning = clubData.sport_sanctionings.includes(this.USAV_SANC_BODY);
            }

            let validationResult = updateSchema.validate(clubData);

            if (validationResult.error) {
                loggers.errors_log.error(validationResult.error);
                throw {
                    validationErrors: validationResult.error.details
                }
            }

            let masterClub = validationResult.value;

            if(masterClub.director_usav_code && masterClub.country !== CA_COUNTRY_CODE) {
                await this.__validateUsavMember(masterClub);
            }

            if (hasUsavSanctioning) {
                let codeExists = await (this._clubCodeExists(masterClub.code, masterClub.region));

                if (codeExists) {
                    throw { validation: 'Club USAV code must be unique within region' };
                }
            }

            if(clubData.has_aau_sanctioning) {
                let codeExists = await (this._clubAauCodeExists(
                    masterClub.aau_club_code, masterClub.aau_primary_membership_id, masterClub.aau_primary_zip));

                if (codeExists) {
                    throw { validation: 'Club AAU code must be unique' };
                }
            }

            // set profile_completed_at = NOW() if it was empty
            if(masterClub.not_completed) {
                delete masterClub.not_completed;
                masterClub.profile_completed_at = moment().format();
            } else {
                delete masterClub.not_completed;
            }

            delete masterClub.has_usav_sanctioning;
            delete masterClub.has_aau_sanctioning;

            tr = await (Db.begin());

            let query = squel.update().table('master_club', 'ms_u').setFields(masterClub)
                .where('ms_u.master_club_id = ?', masterClubId)
                .where('ms_u.club_owner_id = ?', clubOwnerId)
                .returning(`ms_u.club_name, 
                    (SELECT ROW_TO_JSON("old_location_data")
                       FROM (SELECT *
                             FROM old_data od
                             WHERE ms_u.region  <> od.region
                                OR ms_u.city    <> od.city
                                OR ms_u.state   <> od.state
                                OR ms_u.zip     <> od.zip
                                OR ms_u.address <> od.address
                                OR ms_u.country <> od.country) "old_location_data"
                      ) "old_location",
                      (SELECT ROW_TO_JSON("new_location_data")
                       FROM (
                              SELECT ms_u.region, ms_u.city, ms_u.state, ms_u.zip, ms_u.country, ms_u.address
                            ) "new_location_data"
                      ) "new_location"
                `)
                .with('old_data',
                    squel.select().from('master_club', 'ms')
                        .where('ms.master_club_id = ?', masterClubId)
                        .where('ms.club_owner_id = ?', clubOwnerId)
                        .field('ms.region')
                        .field('ms.city')
                        .field('ms.address')
                        .field('ms.state')
                        .field('ms.zip')
                        .field('ms.country')
                );

            let sqlResult = await (tr.query(query));

            if(sqlResult.rowCount < 1) {
                throw {
                    validation: 'Club not Found'
                }
            }


            if(this.clubHasSportSanctionings(clubData)) {
                await this._saveSancAndVars(tr, masterClubId, clubData.sport_sanctionings, []);
            }

            if(hasUsavSanctioning) {
                const {
                    updatedRosterTeams,
                } = await this._updateTeamsCodes(tr, masterClubId, _.pick(masterClub, ['code', 'region']));

                for(const {id: roster_team_id, event_id, old_value, new_value} of updatedRosterTeams) {
                    const params = {
                        action: 'team.data.changed',
                        roster_team_id,
                        comments: `Team code has been changed from ${old_value} to ${new_value}`,
                    };
                    tasks.push(async () => {
                        await eventNotifications.add_notification(event_id, params);
                    });
                }
            }

            await this._updateRosterClubs(tr, masterClubId);

            await tr.commit();
            await Promise.all(tasks.map(f=>f()));

            return sqlResult.rows[0];
        })()
            .catch(err => {
                if(tr && !tr.isCommited) {
                    tr.rollback();
                }
                throw err;
            });
    }

    getChangedLocationData (data) {
	    if(!data.old_location) {
	        return {};
        }

	    let fields = Object.keys(data.old_location);

	    return fields.reduce((all, field) => {
	        if(data.old_location[field] !== data.new_location[field]) {
                all[field] = {
	                old: data.old_location[field],
                    new: data.new_location[field]
                };
            }

	        return all;
        }, {});
    }
}

module.exports = ClubService;
