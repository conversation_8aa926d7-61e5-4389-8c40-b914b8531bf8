{"name": "SW-App", "private": true, "version": "1.0.0", "description": "Sport Wrench application", "dependencies": {"@aws-sdk/client-s3": "^3.842.0", "@aws-sdk/lib-storage": "^3.842.0", "@sailshq/upgrade": "^1.0.9", "amqp-connection-manager": "^4.1.14", "amqplib": "^0.10.7", "apidoc": "^1.2.0", "apidoc-plugin-example": "^0.2.0", "bull": "^4.12.9", "co": "^4.6.0", "connect-redis": "^3.3.2", "cron": "^1.0.5", "date-utils": "^1.2.15", "dotenv": "^8.0.0", "ejs": "^3.1.10", "eventemitter2": "^0.4.14", "express": "^4.19.2", "express-session": "^1.18.1", "express-xml-bodyparser": "^0.4.1", "fast-xml-parser": "^4.3.2", "firebase": "^12.1.0", "geopoint": "^1.0.1", "googlemaps": "^1.12.0", "hashids": "^1.0.2", "html-minifier": "^2.1.0", "htmlparser2": "^4.1.0", "include-all": "^4.0.3", "joi": "^17.2.0", "json-gate": "^0.8.21", "json2xls": "0.0.5", "knex": "^3.1.0", "knox": "^0.9.2", "lodash": "^4.17.21", "moment": "^2.30.1", "moment-timezone": "^0.5.45", "mz": "^2.4.0", "node-fetch": "^2.7.0", "nodemon": "^1.2.0", "nodexml": "^1.0.2", "numeral": "^2.0.6", "optimist": "0.3.4", "passbook": "^2.1.1", "passport": "^0.7.0", "passport-http": "^0.3.0", "passport-local": "^1.0.0", "passport-remember-me": "git+https://github.com/atog/passport-remember-me.git#998c6a0b41a3faafb6a86ba2811490c990b1fa04", "pg": "^8.11.5", "pg-connection-string": "^2.6.4", "pg-cursor": "^2.10.5", "pg-pool": "^3.6.2", "plaid": "^3.0.0", "playwright-chromium": "^1.28.1", "pmx": "^1.6.7", "proxyquire": "^2.1.3", "qr-image": "^3.1.0", "qs": "^6.13.1", "request": "^2.34.0", "request-promise": "^4.2.6", "route-parser": "0.0.5", "sails": "^1.5.11", "sails-hook-sockets": "^3.0.2", "sails-routes-swagger": "^1.1.1", "sharp": "^0.32.6", "sinon": "^7.3.2", "skipper-s3": "^0.6.0", "squel": "5.10.0", "string-format": "^0.5.0", "stripe": "^14.15.0", "twilio": "^3.7.0", "winston": "^3.14.2", "winston-graylog2": "^2.1.2", "winston-syslog": "^2.7.1", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.2/xlsx-0.20.2.tgz", "xlsx-style": "^0.8.13", "xml": "^1.0.1", "xss-escape": "0.0.5", "yargs": "^13.3.0"}, "overrides": {"knox": {"mime": "3.0.0"}}, "scripts": {"start": "node app.js --dev --mult=\"/tickets\"", "debug": "node debug app.js --dev --mult=\"/tickets\"", "scheduler": "NODE_ENV=\"production\" node scheduler/scheduler.worker --prod || exit 0", "scheduler-dev": "NODE_ENV=\"development\" node scheduler/scheduler.worker --dev || exit 0", "test": "mocha --timeout 30000", "migrate-main": "knex --cwd db  --migrations-directory ./migrations/main --connection $SW_DB migrate:latest --client postgresql", "migrate-email": "knex --cwd db  --migrations-directory ./migrations/emailqueue --connection $EMAIL_DB migrate:latest --client postgresql", "migrate-test": "knex --cwd db  --migrations-directory ./migrations/emailqueue --connection $TEST_DB_CONNECTION migrate:latest --client postgresql", "migration-main:create": "knex --cwd db  --migrations-directory ./migrations/main migrate:make", "doc": "apidoc -i ./userconfig/routes -o ./.tmp/doc/", "start:worker-queue": "node worker/main.js --dev", "start:frontend": "webpack serve --config webpack.config.frontend.js", "start:frontend:events": "webpack serve --config webpack.config.frontend_event.js", "start:frontend:admin": "webpack serve --config webpack.config.frontend_admin.js", "build:frontend": "webpack --mode production --config webpack.config.frontend.js", "build:event": "webpack --mode production --config webpack.config.frontend_event.js", "build:admin": "webpack --mode production --config webpack.config.frontend_admin.js"}, "main": "app.js", "repository": "", "author": "SportWrench", "license": "", "homepage": "sportwrench.com", "devDependencies": {"@babel/core": "^7.24.7", "@babel/preset-env": "^7.24.7", "angular-animate": "1.3.20", "angular-bootstrap-colorpicker": "3.0.32", "angular-clipboard": "1.7.0", "angular-cookies": "1.5.8", "angular-dragdrop": "1.0.13", "angular-loading-bar": "0.7.1", "angular-resource": "1.2.32", "angular-sanitize": "1.2.32", "angular-simple-logger": "0.1.7", "angular-toastr": "1.5.0", "angular-ui-bootstrap": "0.13.4", "angular-ui-router": "0.3.1", "angular-ui-utils": "0.1.1", "angular-utils-ui-breadcrumbs": "0.2.2", "angularjs-dropdown-multiselect": "2.0.0-beta.10", "axios": "^1.7.2", "babel-loader": "^9.1.3", "babel-plugin-angularjs-annotate": "^0.10.0", "babel-polyfill": "^6.23.0", "babel-preset-env": "^1.7.0", "babel-runtime": "^6.23.0", "bootstrap-sass": "3.2.0", "bootstrap-ui-datetime-picker": "2.4.3", "chai": "^3.5.0", "chai-as-promised": "^6.0.0", "clean-webpack-plugin": "^4.0.0", "compass-mixins": "0.12.12", "cookie": "^1.0.2", "copy-webpack-plugin": "^12.0.2", "css-loader": "^7.1.2", "font-awesome": "4.7.0", "glob": "^11.0.0", "html-loader": "^5.0.0", "html-webpack-plugin": "^5.6.3", "http-proxy-middleware": "^3.0.5", "inquirer": "^12.9.1", "mini-css-extract-plugin": "^2.9.0", "mocha": "^3.0.1", "ng-infinite-scroll": "1.0.0", "ng-selectable2": "1.0.5", "ng-table": "0.5.4", "ngstorage": "0.3.11", "nock": "^8.0.0", "oclazyload": "0.4.2", "sass": "^1.77.7", "sass-loader": "^14.2.1", "should": "^10.0.0", "sticky-table-headers": "0.1.24", "terser-webpack-plugin": "^5.3.10", "ui-select": "0.19.8", "underscore.string": "^3.3.6", "webpack": "^5.92.1", "webpack-cli": "^5.1.4", "webpack-dev-server": "^5.0.4"}}