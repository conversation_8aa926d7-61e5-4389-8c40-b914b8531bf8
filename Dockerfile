FROM itrdevsw/pm2:22
ARG UNAME=sw
ARG UID
ARG GID
ARG ENV=${NODE_ENV}

RUN if [ "$ENV" = "local" ] ;\
    then npm i nodemon -g ;\
    fi

RUN groupadd -g $GID -o $UNAME
RUN useradd -m -u $UID -g $GID -o -s /bin/bash $UNAME
USER $UNAME

# Create app directory
RUN mkdir -p /home/<USER>/.pm2
RUN mkdir -p /home/<USER>/app
RUN mkdir -p /home/<USER>/logs

# Install pm2-logger module
ARG LOG_APP_ID
ARG LOG_PG_CS
RUN pm2 set pm2-logger:pg_connection_string $LOG_PG_CS
RUN pm2 set pm2-logger:instance_id $LOG_APP_ID
RUN pm2 install git+https://gitlab.uastage.com/misc-public/pm2-logger

WORKDIR /home/<USER>/app

ENV PORT=3000

USER root

#We need to rebuild sharp module for linux in docker
RUN npm install --legacy-peer-deps --platform=linuxmusl --arch=x64 sharp

USER $UNAME

CMD case $NODE_ENV in\
    "production")\
        pm2-runtime start ecosystem-prod.json\
        ;;\
    "development")\
        pm2-runtime start ecosystem-dev.json\
        ;;\
    "local")\
        nodemon app\
        ;;\
    *)\
        echo "Invalid NODE_ENV value: '$NODE_ENV'" ;\
        exit 1\
        ;;\
   esac

# force restart of docker container. 03dd30924
