angular.module('SportWrench')

.constant('USER_OPTS', {
    LOGGED_IN: true,
    STATUS_SPECTATOR: 1,
    STATUS_EVENT_OWNER: 1,
    STATUS_CLUB_DIRECTOR: 1,
    STATUS_VENDOR: 1,
    STATUS_STAFF: 1
})

.constant('SERVER_UNAVAILABLE_MSG', 'Server is currently unavailable. Please try again in a few seconds.')

.constant('FORM_VALIDATION_ERROR_KEY', 'serverValidation')

.constant('INTERNAL_ERROR_MSG', 'Internal Server Error. Please, try again later.')

.constant('MAP_MARKER_IMAGE_PATH', 'images/marker-circle.png')

// ❗️❗️❗️
//
// don't change 'toolbar' and 'toolbar_full' properties
// this can disrupt the behavior of CKEDITOR
.constant('CKEDITOR_OPTIONS', {
    toolbar: '',
    toolbar_full: [],
    language: 'en',
    uiColor: '#D5D5D5',
    toolbarGroups: [
        { name: 'forms', groups: [ 'forms' ] },
        { name: 'document', groups: [ 'mode', 'document', 'doctools' ] },
        { name: 'clipboard', groups: [ 'clipboard', 'undo' ] },
        { name: 'editing', groups: [ 'find', 'selection', 'spellchecker', 'editing' ] },
        '/',
        { name: 'basicstyles', groups: [ 'basicstyles', 'cleanup' ] },
        { name: 'paragraph', groups: [ 'list', 'indent', 'blocks', 'align', 'bidi', 'paragraph' ] },
        { name: 'colors', groups: [ 'colors' ] },
        { name: 'links', groups: [ 'links' ] },
        { name: 'insert', groups: [ 'insert' ] },
        '/',
        { name: 'styles', groups: [ 'styles' ] },
        { name: 'tools', groups: [ 'tools' ] },
        { name: 'others', groups: [ 'others' ] },
        { name: 'about', groups: [ 'about' ] }
    ],
    removeButtons: 'Form,Checkbox,Radio,TextField,Textarea,Select,Button,ImageButton,HiddenField,Language,BidiRtl,BidiLtr,Flash,About',
})

.constant('SOCIAL_PATH', '/images/social_networks/')

.constant('SOCIAL_NETWORKS', {
    facebook:   { img: 'facebook_icon.png',     type: 'url',    name: 'Facebook'    },
    instagram:  { img: 'instagram_icon.png',    type: 'url',    name: 'Instagram'   },
    twitter:    { img: 'twitter_icon.png',      type: 'url',    name: 'Twitter'     },
    snapchat:   { img: 'snapchat_icon.png',     type: 'url',   name: 'Snapchat'    }
})

.constant('SPORT_POSITIONS', [
    { id: 3, name: 'Middle',                short_name: 'MB'    },
    { id: 4, name: 'Right Side',            short_name: 'RS'    },
    { id: 5, name: 'Outside',               short_name: 'OH'    },
    { id: 6, name: 'Setter',                short_name: 'S'     },
    { id: 7, name: 'Libero',                short_name: 'LB'    },
    { id: 8, name: 'Defensive Specialist',  short_name: 'DS'    },
])

.constant('STAFF_ROLES', [
    { id: 2,    name: 'Club Director',          short_name: 'CD' },
    { id: 3,    name: 'Asst Director',          short_name: 'AD' },
    { id: 4,    name: 'Head Coach',             short_name: 'HC' },
    { id: 5,    name: 'Asst Coach',             short_name: 'AC' },
    { id: 6,    name: 'Team Representative',    short_name: 'TR' },
    { id: 7,    name: 'Manager',                short_name: 'M'  },
    { id: 11,   name: 'Recruiting Coordinator', short_name: 'RC' },
    { id: 15,   name: 'Chaperone',              short_name: 'C'  }
])

.constant('OFFICIALS_RATINGS', [
    { id: 'UnRated',         name: 'UnRated'         },
    { id: 'International',   name: 'International'   },
    { id: 'National',        name: 'National'        },
    { id: 'Jr. National',    name: 'Jr. National'    },
    { id: 'Regional' ,       name: 'Regional'        },
    { id: 'Provisional',     name: 'Provisional'     },
    { id: 'Other',           name: 'Other'           },
    { id: 'In Region',       name: 'In Region'       }
])

.constant('MALE_CHAR', String.fromCharCode(9794))

.constant('FEMALE_CHAR', String.fromCharCode(9792))

.constant('GENDER_VALUES', {
    MALE: 'male',
    FEMALE: 'female',
    NON_BINARY: 'non-binary',
})

.constant('URL_PATTERN', /^http(s)?:\/\/[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-z]{1,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/)

.constant('EMAIL_PATTERN', /^\w+(?:[._+-]\w+)*@\w+(?:[_.-]\w+)*\.[a-zA-Z]{2,4}$/)

.constant('INTEGER_PATTERN', /^([0-9]+)$/)

.constant('SUPPORT_REASONS', [
    { id: 'payment',        title: 'Payment Question'               },
    { id: 'withdrawal',     title: 'Withdrawal / Refund Request'    },
    { id: 'divisions',      title: 'Division Change Request'        },
    { id: 'officials',      title: 'Officials Withdrawal'           },
    { id: 'account',        title: 'SportWrench Account Setup'      },
    { id: 'entry',          title: 'Tournament Entry Issues'        },
    { id: 'webpoint',       title: 'Webpoint Import Issues'         },
    { id: 'camps',          title: 'Camp Related Question'          },
    { id: 'coalition',      title: 'Coalition Related Questions'    },
    { id: 'other',          title: 'Other'                          }
])

.constant('OVERWRITE_VALUE_MODE', 'o')

.constant('DEFAULT_VALUE_MODE', 'd')

.constant('CAMPS_SALES_TYPE', 'camps')

.constant('PAYMENT_TYPE', {
    CARD        : 'card',
    CHECK       : 'check',
    CASH        : 'cash',
    ACH         : 'ach',
    FREE        : 'free',
    WAITLIST    : 'waitlist',
    PENDING_PAYMENT: 'pending-payment',
})

.constant('STRIPE_PAYMENT_TYPE', {
    CARD: 'card',
    ACH: 'us_bank_account',
})

.constant('CARD_PAYMENT_SUB_TYPES', [
    'apple_pay',
    'google_pay'
])

.constant('ACH_PAYMENT_SUB_TYPES', [
    'us_bank_account'
])

.constant('ONLINE_PAYMENT_INTERVAL_PARAMS', {
    MAX_RETRIES: 10,
    RETRIES_INTERVAL: 2 * 1000
})

.constant('ONLINE_PAYMENT_MESSAGES', {
    INVALID_FLOW_MESSAGE: 'Something went wrong! Check the list of payments in a ' +
        'few minutes and retry the payment if it is not successful!'
})

.constant('PAYMENT_STATUS', {
    PAID        : 'paid',
    PENDING     : 'pending',
    CANCELED    : 'canceled'
})
.constant('APPLICATION_STATUS', {
    DECLINED: 'declined',
    APPROVED: 'approved',
    PENDING: 'pending',
})
.constant('TEAM_STATUS', {
    PAYMENT: {
        PAID: 22,
        NONE: 21,
        PARTIAL: 23,
        PENDING: 24,
        REFUNDED: 25,
        DISPUTED: 26
    },
})

.constant('TEAM_PAYMENT_STATUS_ARRAY', [
    { title: 'Paid'     , id: 22 },
    { title: 'None'     , id: 21 },
    { title: 'Partial'  , id: 23 },
    { title: 'Pending'  , id: 24 },
    { title: 'Refunded' , id: 25 },
    { title: 'Disputed' , id: 26 }
])

.constant('GMAPS_API_KEY', 'AIzaSyANPQqA7Og1mNaWt9Rzd4Uu5ppD0osrwIQ')

.constant('GMAPS_CLUSTER_IMG_PATH', 'images/markers/')

.constant('ZIP_REG_EXP', /(^([0-9]{5})$)|(^(?!.*[DFIOQU])[A-VXY][0-9][A-Z] [0-9][A-Z][0-9]$)/i)

.constant('INVALID_FORM_ERROR_MSG', 'Invalid Form Data')

.constant('USAV_PATTERN', /^([A-Z]{2}\d{7}[A-Z]{2,3}\d{2,3}|\d{7})$/i)

.constant('ACTIVATION_SUCCEED', 'You have been activated. Please log in to proceed')

.constant('ACTIVATION_FAILED', 'Invalid activation URL')

.constant('DONE_MSG', 'Done!')

.constant('SAVED_MSG', 'Saved!')

.constant('CAMPS_TICKETS_FILTER_NAME', 'camps')

.constant('TICKETS_FILTER_NAME', 'tickets')

.constant('QUALIFIED_DIVISIONS', [
    { id: 'open'    , title: 'Open'},
    { id: 'american', title: 'American'},
    { id: 'national', title: 'National'},
    { id: 'usa'     , title: 'USA'},
    { id: 'liberty' , title: 'Liberty'},
    { id: 'freedom' , title: 'Freedom'},
    ]
)

.constant('PAYMENT_OPTION_LABEL', {
    'direct_deposit'    : { label: 'Direct Deposit' },
    'on_site'           : { label: 'On Site'        },
    'mailed'            : { label: 'Mailed'         },
    'arbiterpay'        : { label: 'ArbiterPay'     },
    'no_payment_required': { label: 'No Payment Required' }
})

.constant('PAYMENT_OPTIONS', {
    directDeposit: 'direct_deposit',
    onSite: 'on_site',
    mailed: 'mailed',
    arbiterPay: 'arbiterpay',
    rqPay: 'rq_pay',
    noPaymentRequired: 'no_payment_required',
})

.constant('QUALIFIER_EVENT_TYPE', 'qualifier')
.constant('OTHER_EVENT_TYPE', 'other')

.constant('STAFF_MEMBER_TYPE', 'staff')
.constant('OFFICIAL_MEMBER_TYPE', 'official')

.constant('AVAILABLE_OFFICIALS_SANCTIONINGS', {
    USAV: "usav",
    AAU: "aau",
    UNSANCTIONED: "unsanctioned"
})
.constant('ROSTER_CHANGE_WARNING_TEXT',
    `Warning: Changes here will update the roster of teams entered into events BUT won't affect locked team's roster.`
)

.constant('CHECKIN_MODES', {
    PRIMARY_STAFF_BARCODES  : 'primary_staff_barcodes',
    DEFAULT                 : 'default'
})

.constant('ONLINE_CHECKIN_MESSAGES', {
    PRIMARY_STAFF_BARCODES: `An email will be sent to each Primary Staff member with further instructions
        for admission to the event.`,
    DEFAULT               : `A email has been sent to the authorized parties containing a barcode that is required
        to pickup your wristbands`
})

.constant('EMAIL_RECEIVER_TYPE', {
    CLUB_DIRECTOR: 'cd',
    STAFF        : 'staff'
})

.constant('SET_USER_NAME', 'setUserName')

.constant('SANCTIONING_BODY', {
    AAU: 1,
    JVA: 2,
    USAV: 3,
    OTHER: 7,
    NINE_MAN: 9,
})

.constant('ENTRY_STATUS', [
    { title: 'Pending'  , id: 13 },
    { title: 'Declined' , id: 11 },
    { title: 'Accepted' , id: 12 },
    { title: 'Wait list', id: 14 },
])

.constant('TRAVEL_METHOD', {
    car 	: 'Car',
    air 	: 'Air',
    other 	: 'Other'
})


.constant('ZIP_REG_EXP_CANADA', /^[a-zA-Z]\d[a-zA-Z]\s\d[a-zA-Z]\d$/)
.constant('ZIP_REG_EXP_US', /^([0-9]{5})$/)
.constant('ZIP_REG_EXP_CO', /^([0-9]{6})$/)

.constant('EVENT_OWNERS', {
    group: 'event.owners',
    section: 'event.owners',
})

.constant('CLUBS_DIRECTORS_GROUP', 'clubs_directors')

.constant('EVENT_OPERATIONS', {
    TEAMS_TAB                : 'teams_tab',
    EDIT_EVENT               : 'edit_event',
    DIVISIONS_TAB            : 'divisions_tab',
    CHECKIN_TAB              : 'checkin_tab',
    EMAIL_MODULE_TAB         : 'email_module_tab',
    ACCOUNTING_TAB           : 'accounting_tab',
    TICKETS_TAB              : 'tickets_tab',
    OFFICIALS_TAB            : 'officials_tab',
    STAFF_TAB                : 'staff_tab',
    EXHIBITORS_TAB           : 'exhibitors_tab',
    EVENT_INFO_TAB           : 'event_info_tab',
    HISTORY_TAB              : 'history_tab',
    CUSTOM_FORMS             : 'custom_forms',

    TICKET_SETTINGS_TAB      : 'ticket_settings_tab',
    TICKET_PAYMENTS_LIST_TAB : 'ticket_payments_list_tab',
    TICKET_MAP_TAB           : 'ticket_map_tab',
    TICKET_STATISTICS_TAB    : 'ticket_statistics_tab',
    TICKET_DISCOUNTS_TAB     : 'ticket_discounts_tab',
    TICKET_APPLICATION_APPROVE_TAB: 'ticket_application_approve_tab'
})

.constant('USER_ROLE', {
    CD      : 'club_director',
    OFFICIAL: 'official'
})

.constant('SANCTIONING_CHECK_FIELDS_ALIASES', {
    safesport: {
        manual_ok   : 'safesport_manual_ok',
        expiration  : 'safesport_expiration',
        status      : 'safesport_status',
        exp_date    : 'safesport_end_date'
    },
    aau_safesport: {
        manual_ok   : 'aau_safesport_manual_ok',
        expiration  : 'aau_safesport_expiration',
        status      : 'aau_safesport_status',
        exp_date    : 'aau_safesport_end_date'
    },
    background: {
        manual_ok   : 'bg_manual_ok',
        expiration  : 'bg_expiration',
        status      : 'background_screening',
        exp_date    : 'bg_expire_date'
    },
    aau_background: {
        manual_ok   : 'aau_bg_manual_ok',
        expiration  : 'aau_bg_expiration',
        status      : 'aau_bg_screening',
        exp_date    : 'aau_bg_expire_date'
    },
    membership: {
        expiration  : 'mbr_expiration',
        exp_date    : 'mbr_expire_date',
        status      : 'usav_num'
    },
    aau_membership: {
        expiration  : 'aau_expiration',
        exp_date    : 'aau_expire_date',
        status      : 'aau_number'
    },
})

.constant('SAFESPORT_FIELD', 'safesport')
.constant('BG_FIELD', 'background')
.constant('MBR_FIELD', 'membership')
.constant('ACTIVATE', 'activate')
.constant('DEACTIVATE', 'deactivate')
.constant('AAU_FIELD', 'aau_membership')
.constant('AAU_BG_FIELD', 'aau_background')
.constant('AAU_SAFESPORT_FIELD', 'aau_safesport')
.constant('CLUB_ADDRESSES_BLACKLIST_REGEXES', [
    /(^|\s)po[\s-]*box(\s|$)/,
    /(^|\s)p o box(\s|$)/,
    /(^|\s)box po(\s|$)/,
    /(^|\s)box p o(\s|$)/,
    /(^|\s)suite box(\s|$)/,
    /(^|\s)box suite(\s|$)/,
    /(^|\s)p office box(\s|$)/,
    /(^|\s)office box(\s|$)/,
    /(^|\s)post office box(\s|$)/,
    /(^|\s)mail[\s-]*box(\s|$)/,
    /^box\s?[0-9]*$/
])
.constant('SHOW_OFFICIALS_PAYOUTS_TAB_ACTION', 'show_officials_payouts_tab_action')
.constant('SHOW_STAFF_PAYOUTS_TAB_ACTION', 'show_staff_payouts_tab_action')
.constant('LOAD_PAYOUTS_ACTION', 'load_payouts_action')
.constant('FEE_PAYER', {
    BUYER   : 'buyer',
    SELLER  : 'seller'
})
.constant('PAYMENT_PROVIDER', {
    STRIPE  : 'stripe',
    TILLED  : 'tilled',
    PAYMENT_HUB: 'payment-hub',
})
.constant('CAMP_REGISTRATION_STATUS', {
    ACTIVE  : 'active',
    CANCELED: 'canceled'
})
.constant('EXHIBITOR_EVENT_TABS', {
    EVENT_INFO: 'event_info',
    REGISTRATION_INFO: 'registration_info'
})
.constant('EXHIBITOR_EVENT_STATUS', {
    PENDING: 'pending',
    APPROVED: 'approved',
    DECLINED: 'declined',
})
.constant('EVENT_EXHIBITOR_MODAL_TABS', {
    EXHIBITOR_INFO: 'exhibitor_info',
    APPLICATION_INFO: 'application_info'
})
.constant('SAFESPORT_VALID_STATUS', 2)
.constant('SAFESPORT_NOT_VALID_STATUS', 1)
.constant('ATHLETE_MEMBER_TYPE', 'athlete')
.constant('SAFESPORT_STATUS_LABEL', {
    VALID: 'YES',
    NOT_VALID: 'NO',
})
.constant('TEAM_ENTRY_STATUS', {
    PENDING: 13,
    DECLINED: 11,
    ACCEPTED: 12,
    WAITLIST: 14,
})
.constant('OTHER_BOOTH_LABEL', 'Other')
.constant('OTHER_BOOTH', {
    id: 0
})
.constant('DEFAULT_OTHER_BOOTH', {
    quantity: 1,
    fee: 0,
    event_booth_id: null,
    amount: 0,
    title: 'Other',
    description: '10x10',
    booth_label: '',
    notes: '',
})
.constant('IMPORT_STATUSES', {
    RUNNING: 'running',
    FINISHED: 'finished',
    ERROR: 'error',
})
.constant('MIN_PAYMENT_INTENT_AMOUNT', 0.5)
.constant('MIN_AMOUNT_FOR_ACH', 100)
.constant('PAYMENT_INTENT_STATUS', {
    SUCCEEDED: 'succeeded',
    PROCESSING: 'processing',
    REQUIRES_PAYMENT_METHOD: 'requires_payment_method'
})
//http://www.faqs.org/rfcs/rfc2822.html 2.2.1 Line Length Limits
.constant('MAX_EMAIL_SUBJECT_LENGTH', 998)
.constant('BID_AGREEMENT_SENDER', {
    CLUB: 'club',
    EVENT: 'event'
})
.constant('TEAM_AGREEMENT_VALUE', {
    ACCEPTED: 'accepted',
    DECLINED: 'declined'
})
.constant('OFFICIALS_PAYOUT_MEMBER_TYPE', 'officials')
.constant('USAV_SEASONALITY', {
    FULL: 'full',
    LOCAL: 'local'
})
.constant('ATHLETE_ROLES', [
    { id: 0, name: 'Player' },
    { id: 1, name: 'Team Captain'}
])
.constant('TEAMS_SW_FEE_COLLECTION_MODE', {
    AUTO: 'auto',
    MANUAL: 'manual',
    AUTO_CARD: 'auto_card'
})
.constant('CUSTOM_FORM_TYPE', {
    CAMPS_PURCHASE_PAGE: 'camps_purchase_page',
    TEAM_ASSIGN_FOR_EVENT: 'team_assign_for_event'
})
.constant('TICKET_BARCODE_BACKGROUND_COLOR', [
    { title: 'Green', value: 'green' },
    { title: 'Yellow', value: 'yellow' },
    { title: 'Blue', value: 'blue' },
    { title: 'Red', value: 'red' },
    { title: 'Violet', value: 'violet' },
])
.constant('ONLINE_CHECKIN_HISTORY_ACTION_TYPE', {
    SCAN: 'scan',
    REENTRY: 'reentry'
})
.constant('MERCHANDISE_TYPE', [
    { title: 'T-shirt', value: 't-shirt' },
    { title: 'Sweatshirt', value: 'sweatshirt' },
    { title: 'Hat', value: 'hat'},
    { title: 'Tank Top', value: 'tank-top' },
    { title: 'Keychain', value: 'keychain' },
    { title: 'Socks', value: 'socks' },
    { title: 'Shorts', value: 'shorts' },
    { title: 'Sweatpants', value: 'sweatpants' },
    { title: 'Other', value: 'other' },
])
.constant('PAYMENT_ACCOUNT_TYPE', {
    TICKETS: 'tickets',
    EVENT: 'event'
})
.constant('OFFICIAL_SCAN_HISTORY_ACTION_TYPE', {
    SCAN: 'scan',
    REENTRY: 'reentry'
})
.constant('COUNTRY_CODES', {
    UNITED_STATES: 'US',
    CANADA: 'CA',
    POLAND: 'PL',
    TAIWAN: 'TW',
    HONDURAS: 'HN',
    COLOMBIA: 'CO',
})
.constant('DB_DATETIME_FORMAT', 'MM/DD/YYYY, h:mm a')
.constant('MONTH_DAY_DATETIME_FORMAT', 'MM/DD h:mm a')
.constant('EVENT_DATES_FORMAT', 'MMM D, YYYY')
.constant('EXHIBITOR_LIST_DATES_FORMAT', 'MMM d, yyyy')
.constant('NG_TABLE_TOOLTIPS', {
    REFUNDED: 'refunded'
})
.constant('PAYMENT_PROVIDER', {
    PAYMENT_HUB: 'payment-hub',
    STRIPE: 'stripe'
})

.constant('EVENTS_WHERE_OFFICIAL_PAYOUTS_HIDDEN_FROM_HO', [24718, 24717])

.constant('PAYMENT_CONSTANTS', {
    MINIMUM_TICKET_MARGIN: 1,
    DEFAULT_STRIPE_PERCENT: 2.9,
    DEFAULT_STRIPE_FIXED: 0.30,
})

.constant('IMPORT_MEMBERS_RESTRICTION_DATE','2025-09-01');
